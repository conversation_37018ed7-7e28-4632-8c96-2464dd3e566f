{
    // Enable format on save
    "editor.formatOnSave": true,
    "editor.formatOnSaveMode": "file",
    // Black settings
    "python.formatting.blackArgs": [
        "--line-length",
        "88"
    ],
    // isort settings (import sorting compatible with Black)
    "python.sortImports.args": [
        "--profile",
        "black"
    ],
    // Enable linting
    "python.linting.enabled": true,
    "python.linting.flake8Enabled": true
}{
    "editor.formatOnSave": true,
    "editor.formatOnType": false,
    "editor.formatOnPaste": false,
    "[python]": {
        "editor.defaultFormatter": "ms-python.python",
        "editor.formatOnSave": true,
        "editor.codeActionsOnSave": {
            "source.organizeImports": "explicit"
        }
    },
    "black-formatter.args": [
        "--line-length=88",
        "--target-version=py39"
    ],
    "python.analysis.autoImportCompletions": true,
    "python.analysis.typeCheckingMode": "basic",
    "python.defaultInterpreterPath": "./.venv/bin/python"
}